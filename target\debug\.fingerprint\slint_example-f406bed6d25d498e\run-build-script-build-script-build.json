{"rustc": 12488743700189009532, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7672247367135371553, "build_script_build", false, 8238988545625849797]], "local": [{"RerunIfChanged": {"output": "debug\\build\\slint_example-f406bed6d25d498e\\output", "paths": ["target\\debug\\build\\SLINT_DEFAULT_STYLE.txt", "ui/appwindow.slint"]}}, {"RerunIfEnvChanged": {"var": "SLINT_STYLE", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_FONT_SIZES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_SCALE_FACTOR", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_ASSET_SECTION", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMBED_RESOURCES", "val": null}}, {"RerunIfEnvChanged": {"var": "SLINT_EMIT_DEBUG_INFO", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}