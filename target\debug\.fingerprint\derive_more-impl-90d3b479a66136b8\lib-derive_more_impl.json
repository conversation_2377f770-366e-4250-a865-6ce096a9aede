{"rustc": 12488743700189009532, "features": "[\"add\", \"add_assign\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"into\", \"mul\", \"not\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 3137586431867243187, "deps": [[3060637413840920116, "proc_macro2", false, 15031816331906695965], [16126285161989458480, "unicode_xid", false, 10306991003522019492], [17990358020177143287, "quote", false, 13277401334686030810], [18149961000318489080, "syn", false, 13640114102946474367]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-90d3b479a66136b8\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}