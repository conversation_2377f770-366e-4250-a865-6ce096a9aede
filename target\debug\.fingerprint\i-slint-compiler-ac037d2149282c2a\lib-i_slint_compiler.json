{"rustc": 12488743700189009532, "features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"software-renderer\"]", "declared_features": "[\"bundle-translations\", \"codemap\", \"codemap-diagnostic\", \"cpp\", \"default\", \"display-diagnostics\", \"fontdue\", \"image\", \"proc-macro2\", \"proc_macro_span\", \"quote\", \"rust\", \"sdf-fonts\", \"software-renderer\"]", "target": 5997077308893045496, "profile": 2225463790103693989, "path": 18416152397788534698, "deps": [[164398694462155020, "typed_index_collections", false, 6555367185103880140], [925920622245451495, "strum", false, 9721466816344347303], [1470679118034951355, "num_enum", false, 8636470523360660427], [1504670837852841510, "rowan", false, 1361823795598083749], [3046214465055776457, "fontdue", false, 5937619241235136473], [3060637413840920116, "proc_macro2", false, 15031816331906695965], [3150220818285335163, "url", false, 3317386328290682353], [4264449604758607596, "resvg", false, 17694083279180078783], [5560038387766815647, "codemap", false, 8248896144770928792], [7379558455692600769, "by_address", false, 5915823664848929347], [7611461696084798871, "lyon_path", false, 10471282168432703337], [7721950305594023387, "codemap_diagnostic", false, 3877421747872551942], [7864482126583800221, "linked_hash_set", false, 10355933041403526576], [8317047369197848296, "polib", false, 6126888904866190976], [9129745186230991347, "smol_str", false, 683890550613651723], [10697383615564341592, "rayon", false, 6100990207767139440], [11293676373856528358, "derive_more", false, 2616408310091632263], [13011651213356413521, "lyon_extra", false, 18250395642610754787], [13028763805764736075, "image", false, 8241070857392305994], [14533926308594949856, "build_script_build", false, 11530933883255898628], [16326338539882746041, "itertools", false, 12075081154696107220], [16415649488169917901, "i_slint_common", false, 6219072596704142460], [17990358020177143287, "quote", false, 13277401334686030810]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-compiler-ac037d2149282c2a\\dep-lib-i_slint_compiler", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}