{"rustc": 12488743700189009532, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 2241668132362809309, "path": 1219209512976785871, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 14423091425088939916], [5756138707274668043, "i_slint_core_macros", false, 4111551129978538096], [7613193051436156431, "i_slint_core", false, 9552663978378225884], [7649432603155880922, "build_script_build", false, 15819534145942511052], [10411997081178400487, "cfg_if", false, 15669488323948584752], [17699788962609858224, "i_slint_backend_winit", false, 5652327638351308948]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-f63aad25325b497b\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}