{"rustc": 12488743700189009532, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"async\", \"default\", \"futures\", \"std\"]", "target": 8369499057004385739, "profile": 2225463790103693989, "path": 12888314357301186670, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\weezl-aac1ed8e158684bb\\dep-lib-weezl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}