import { Button, VerticalBox, HorizontalBox, <PERSON>View, TextEdit, LineEdit } from "std-widgets.slint";

export component AppWindow inherits Window {
    width: 1200px;
    height: 800px;
    title: "MMerge Hack Tool";

    // Properties for data binding
    property <[string]> file_list: [];
    property <string> hero_text: "";
    property <string> search_text: "";
    property <string> file_name: "No file selected";
    property <string> file_content_hex: "";

    // Callbacks
    callback pick_folder();
    callback open_hero_file();
    callback erase_events();
    callback save_hero();
    callback find_hero();
    callback extract_selection_as_hero();
    callback place_hero_at_cursor();
    callback open_file();
    callback save_file();
    callback save_file_as();

    HorizontalBox {
        spacing: 10px;
        padding: 10px;

        // Left Column
        VerticalBox {
            width: 33%;
            spacing: 10px;

            Button {
                text: "Pick Folder";
                clicked => { root.pick_folder(); }
            }

            // File list - 30% of screen height
            Rectangle {
                height: 240px;
                border-width: 1px;
                border-color: #ccc;

                ListView {
                    for file in root.file_list: Text {
                        text: file;
                        font-size: 12px;
                    }
                }
            }

            Button {
                text: "Open Hero File";
                clicked => { root.open_hero_file(); }
            }

            // Text window - 30% of screen height
            Rectangle {
                height: 240px;
                border-width: 1px;
                border-color: #ccc;

                TextEdit {
                    text <=> root.hero_text;
                    font-size: 12px;
                    wrap: word-wrap;
                }
            }

            Button {
                text: "Erase Events";
                clicked => { root.erase_events(); }
            }

            Button {
                text: "Save Hero";
                clicked => { root.save_hero(); }
            }
        }

        // Center Column
        VerticalBox {
            width: 33%;
            spacing: 20px;
            alignment: center;

            Button {
                text: "Find Hero";
                clicked => { root.find_hero(); }
            }

            LineEdit {
                text <=> root.search_text;
                placeholder-text: "Enter search text...";
            }

            Button {
                text: "Extract Selection as Hero";
                clicked => { root.extract_selection_as_hero(); }
            }

            Button {
                text: "Place Hero at Cursor";
                clicked => { root.place_hero_at_cursor(); }
            }
        }

        // Right Column
        VerticalBox {
            width: 33%;
            spacing: 10px;

            Button {
                text: "Open File";
                clicked => { root.open_file(); }
            }

            Text {
                text: root.file_name;
                font-size: 12px;
                horizontal-alignment: center;
            }

            // File content in hex format - takes remaining vertical space
            Rectangle {
                border-width: 1px;
                border-color: #ccc;

                TextEdit {
                    text <=> root.file_content_hex;
                    font-size: 10px;
                    read-only: true;
                    wrap: word-wrap;
                }
            }

            Button {
                text: "Save File";
                clicked => { root.save_file(); }
            }

            Button {
                text: "Save File As";
                clicked => { root.save_file_as(); }
            }
        }
    }
}
