import { Button, VerticalBox } from "std-widgets.slint";

export component AppWindow inherits Window {
    width: 300px;
    height: 120px;
    property <string> label_text: "Hello, Slint!";

    VerticalBox {
        alignment: center;
        spacing: 16px;

        Text {
            text: root.label_text;
            font-size: 20px;
            horizontal-alignment: center;
        }

        But<PERSON> {
            text: "Нажми меня!";
            clicked => {
                root.label_text = "Спасибо за клик!";
            }
        }
    }
}
