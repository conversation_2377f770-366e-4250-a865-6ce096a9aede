{"rustc": 12488743700189009532, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 3385457678612924318, "deps": [[16415649488169917901, "i_slint_common", false, 6219072596704142460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-9fd07c379e70937f\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}