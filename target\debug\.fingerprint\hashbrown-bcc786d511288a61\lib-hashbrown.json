{"rustc": 12488743700189009532, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\", \"rayon\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2225463790103693989, "path": 10404371187439769267, "deps": [[5230392855116717286, "equivalent", false, 5762496573880453942], [9150530836556604396, "allocator_api2", false, 10232833824337210519], [10697383615564341592, "rayon", false, 6100990207767139440], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 9274337020860274656]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-bcc786d511288a61\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}