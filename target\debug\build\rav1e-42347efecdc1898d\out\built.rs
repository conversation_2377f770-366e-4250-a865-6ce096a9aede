//
// EVERYTHING BELOW THIS POINT WAS AUTO-GENERATED DURING COMPILATION. DO NOT MODIFY.
//
#[doc=r#"The Continuous Integration platform detected during compilation."#]
#[allow(dead_code)]
pub static CI_PLATFORM: Option<&str> = None;
#[doc=r#"The full version."#]
#[allow(dead_code)]
pub static PKG_VERSION: &str = "0.7.1";
#[doc=r#"The major version."#]
#[allow(dead_code)]
pub static PKG_VERSION_MAJOR: &str = "0";
#[doc=r#"The minor version."#]
#[allow(dead_code)]
pub static PKG_VERSION_MINOR: &str = "7";
#[doc=r#"The patch version."#]
#[allow(dead_code)]
pub static PKG_VERSION_PATCH: &str = "1";
#[doc=r#"The pre-release version."#]
#[allow(dead_code)]
pub static PKG_VERSION_PRE: &str = "";
#[doc=r#"A colon-separated list of authors."#]
#[allow(dead_code)]
pub static PKG_AUTHORS: &str = "<PERSON>ede <<EMAIL>>";
#[doc=r#"The name of the package."#]
#[allow(dead_code)]
pub static PKG_NAME: &str = "rav1e";
#[doc=r#"The description."#]
#[allow(dead_code)]
pub static PKG_DESCRIPTION: &str = "The fastest and safest AV1 encoder";
#[doc=r#"The homepage."#]
#[allow(dead_code)]
pub static PKG_HOMEPAGE: &str = "";
#[doc=r#"The license."#]
#[allow(dead_code)]
pub static PKG_LICENSE: &str = "BSD-2-Clause";
#[doc=r#"The source repository as advertised in Cargo.toml."#]
#[allow(dead_code)]
pub static PKG_REPOSITORY: &str = "https://github.com/xiph/rav1e/";
#[doc=r#"The target triple that was being compiled for."#]
#[allow(dead_code)]
pub static TARGET: &str = "x86_64-pc-windows-msvc";
#[doc=r#"The host triple of the rust compiler."#]
#[allow(dead_code)]
pub static HOST: &str = "x86_64-pc-windows-msvc";
#[doc=r#"`release` for release builds, `debug` for other builds."#]
#[allow(dead_code)]
pub static PROFILE: &str = "debug";
#[doc=r#"The compiler that cargo resolved to use."#]
#[allow(dead_code)]
pub static RUSTC: &str = "rustc";
#[doc=r#"The documentation generator that cargo resolved to use."#]
#[allow(dead_code)]
pub static RUSTDOC: &str = "rustdoc";
#[doc=r#"Value of OPT_LEVEL for the profile used during compilation."#]
#[allow(dead_code)]
pub static OPT_LEVEL: &str = "0";
#[doc=r#"The parallelism that was specified during compilation."#]
#[allow(dead_code)]
pub static NUM_JOBS: u32 = 12;
#[doc=r#"Value of DEBUG for the profile used during compilation."#]
#[allow(dead_code)]
pub static DEBUG: bool = false;
#[doc=r#"The features that were enabled during compilation."#]
#[allow(dead_code)]
pub static FEATURES: [&str; 1] = ["THREADING"];
#[doc=r#"The features as a comma-separated string."#]
#[allow(dead_code)]
pub static FEATURES_STR: &str = "THREADING";
#[doc=r#"The features as above, as lowercase strings."#]
#[allow(dead_code)]
pub static FEATURES_LOWERCASE: [&str; 1] = ["threading"];
#[doc=r#"The feature-string as above, from lowercase strings."#]
#[allow(dead_code)]
pub static FEATURES_LOWERCASE_STR: &str = "threading";
#[doc=r#"The output of `rustc -V`"#]
#[allow(dead_code)]
pub static RUSTC_VERSION: &str = "rustc 1.85.1 (4eb161250 2025-03-15)";
#[doc=r#"The output of `rustdoc -V`; empty string if `rustdoc -V` failed to execute"#]
#[allow(dead_code)]
pub static RUSTDOC_VERSION: &str = "rustdoc 1.85.1 (4eb161250 2025-03-15)";
#[doc=r#"The target architecture, given by `CARGO_CFG_TARGET_ARCH`."#]
#[allow(dead_code)]
pub static CFG_TARGET_ARCH: &str = "x86_64";
#[doc=r#"The endianness, given by `CARGO_CFG_TARGET_ENDIAN`."#]
#[allow(dead_code)]
pub static CFG_ENDIAN: &str = "little";
#[doc=r#"The toolchain-environment, given by `CARGO_CFG_TARGET_ENV`."#]
#[allow(dead_code)]
pub static CFG_ENV: &str = "msvc";
#[doc=r#"The OS-family, given by `CARGO_CFG_TARGET_FAMILY`."#]
#[allow(dead_code)]
pub static CFG_FAMILY: &str = "windows";
#[doc=r#"The operating system, given by `CARGO_CFG_TARGET_OS`."#]
#[allow(dead_code)]
pub static CFG_OS: &str = "windows";
#[doc=r#"The pointer width, given by `CARGO_CFG_TARGET_POINTER_WIDTH`."#]
#[allow(dead_code)]
pub static CFG_POINTER_WIDTH: &str = "64";
//
// EVERYTHING ABOVE THIS POINT WAS AUTO-GENERATED DURING COMPILATION. DO NOT MODIFY.
//
