{"rustc": 12488743700189009532, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 600225532800476200, "profile": 2225463790103693989, "path": 7074834873720754899, "deps": [[3060637413840920116, "proc_macro2", false, 15031816331906695965], [15203748914246919255, "proc_macro_crate", false, 7681172570301782370], [17990358020177143287, "quote", false, 13277401334686030810], [18149961000318489080, "syn", false, 13640114102946474367]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\num_enum_derive-2a44948b092b2ac0\\dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}