{"rustc": 12488743700189009532, "features": "[\"default\", \"procmacros\", \"profiling-procmacros\"]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 2225463790103693989, "path": 12211316265006550502, "deps": [[8245969239849424265, "profiling_procmacros", false, 11096501692685788525]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\profiling-e6bb14c3a9107ed1\\dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}