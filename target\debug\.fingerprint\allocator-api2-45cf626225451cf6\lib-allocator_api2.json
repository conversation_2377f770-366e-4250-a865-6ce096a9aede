{"rustc": 12488743700189009532, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 8277339565235241299, "path": 1627825973334493260, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\allocator-api2-45cf626225451cf6\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}