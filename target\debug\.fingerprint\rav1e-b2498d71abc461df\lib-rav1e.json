{"rustc": 12488743700189009532, "features": "[\"threading\"]", "declared_features": "[\"aom-sys\", \"asm\", \"av-metrics\", \"backtrace\", \"bench\", \"binaries\", \"byteorder\", \"capi\", \"cc\", \"channel-api\", \"check_asm\", \"clap\", \"clap_complete\", \"console\", \"crossbeam\", \"dav1d-sys\", \"decode_test\", \"decode_test_dav1d\", \"default\", \"desync_finder\", \"dump_ivf\", \"dump_lookahead_data\", \"fern\", \"git_version\", \"image\", \"ivf\", \"nasm-rs\", \"nom\", \"quick_test\", \"scan_fmt\", \"scenechange\", \"serde\", \"serde-big-array\", \"serialize\", \"signal-hook\", \"signal_support\", \"threading\", \"toml\", \"tracing\", \"tracing-chrome\", \"tracing-subscriber\", \"unstable\", \"wasm\", \"wasm-bindgen\", \"y4m\"]", "target": 12405811532001061035, "profile": 2225463790103693989, "path": 9484620777497445016, "deps": [[1895578031732481377, "profiling", false, 2671754263044101424], [2687729594444538932, "debug_unreachable", false, 16225580261620826852], [2924422107542798392, "libc", false, 2197551019705958475], [3722963349756955755, "once_cell", false, 11969783575533502350], [5157631553186200874, "num_traits", false, 6410268884528698991], [5237962722597217121, "simd_helpers", false, 5481389125000075351], [5626665093607998638, "build_script_build", false, 4795907431274049110], [5986029879202738730, "log", false, 1287299980521771848], [7074416887430417773, "av1_grain", false, 2625534847148030355], [8008191657135824715, "thiserror", false, 4660558640139144988], [10411997081178400487, "cfg_if", false, 13623197462959307250], [11063920846464372013, "v_frame", false, 16417326568071861481], [11263754829263059703, "num_derive", false, 2540119320036000461], [12672448913558545127, "noop_proc_macro", false, 10916651243559315676], [13847662864258534762, "arrayvec", false, 13219420139254980847], [14931062873021150766, "itertools", false, 16420335380170327666], [16507960196461048755, "rayon", false, 11195350625794037358], [17605717126308396068, "paste", false, 2130589511604383241], [17706129463675219700, "arg_enum_proc_macro", false, 1446739785240535474], [17933778289016427379, "bitstream_io", false, 5430034633500780600]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rav1e-b2498d71abc461df\\dep-lib-rav1e", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}