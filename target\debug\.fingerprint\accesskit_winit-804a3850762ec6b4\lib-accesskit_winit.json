{"rustc": 12488743700189009532, "features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_06\"]", "declared_features": "[\"accesskit_unix\", \"async-io\", \"default\", \"rwh_05\", \"rwh_06\", \"tokio\"]", "target": 14667884907678119804, "profile": 15657897354478470176, "path": 14806015053565179181, "deps": [[3666421787376679933, "accesskit", false, 7713286895675317683], [4143744114649553716, "rwh_06", false, 6292064232645122922], [5877456377495598509, "winit", false, 2395944516520511019], [5932064766349207815, "accesskit_windows", false, 10173484849393065831]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\accesskit_winit-804a3850762ec6b4\\dep-lib-accesskit_winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}