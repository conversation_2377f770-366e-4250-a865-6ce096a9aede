cargo:rerun-if-changed=D:\Dev\Rust\mmmerge_hack\target\debug\build\SLINT_DEFAULT_STYLE.txt
cargo:rerun-if-changed=D:\Dev\Rust\mmmerge_hack\ui/appwindow.slint
cargo:rerun-if-env-changed=SLINT_STYLE
cargo:rerun-if-env-changed=SLINT_FONT_SIZES
cargo:rerun-if-env-changed=SLINT_SCALE_FACTOR
cargo:rerun-if-env-changed=SLINT_ASSET_SECTION
cargo:rerun-if-env-changed=SLINT_EMBED_RESOURCES
cargo:rerun-if-env-changed=SLINT_EMIT_DEBUG_INFO
cargo:rustc-env=SLINT_INCLUDE_GENERATED=D:\Dev\Rust\mmmerge_hack\target\debug\build\slint_example-299f20c9a2f7e8dc\out\appwindow.rs
