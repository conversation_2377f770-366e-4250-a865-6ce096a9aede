{"rustc": 12488743700189009532, "features": "[\"accessibility\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-qt\", \"i-slint-backend-winit\", \"renderer-femtovg\", \"renderer-software\"]", "declared_features": "[\"accessibility\", \"backend-linuxkms\", \"backend-linuxkms-noseat\", \"backend-qt\", \"backend-winit\", \"backend-winit-wayland\", \"backend-winit-x11\", \"default\", \"i-slint-backend-linuxkms\", \"i-slint-backend-qt\", \"i-slint-backend-testing\", \"i-slint-backend-winit\", \"i-slint-renderer-skia\", \"raw-window-handle-06\", \"renderer-femtovg\", \"renderer-skia\", \"renderer-skia-opengl\", \"renderer-skia-vulkan\", \"renderer-software\", \"rtti\", \"system-testing\"]", "target": 17512649618174676683, "profile": 15657897354478470176, "path": 1219209512976785871, "deps": [[2134775856264564058, "i_slint_backend_qt", false, 17671164577979555629], [5756138707274668043, "i_slint_core_macros", false, 4111551129978538096], [7613193051436156431, "i_slint_core", false, 337798894358651744], [7649432603155880922, "build_script_build", false, 16532345088804303763], [10411997081178400487, "cfg_if", false, 2753012119403523535], [17699788962609858224, "i_slint_backend_winit", false, 3377922292773019092]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\i-slint-backend-selector-a634bcce77cd823b\\dep-lib-i_slint_backend_selector", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}